{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(ar|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "0VGyFPE9KAypXhnKYSkLX", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gQtcWOkKyTbjvKWoWIRXms23gY5ZlpwVZ3saBo/HHX0=", "__NEXT_PREVIEW_MODE_ID": "f2589eff7d25b1fd4208e70967aea8d7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "083c97498b5fdf44a0779a7acb7230484665106380a7de3bd918189461ddfe13", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4e67e9874ec7ea9b6226121453f1290fb8cf2ea0e47b2e441046ad919a1bea2a"}}}, "functions": {}, "sortedMiddleware": ["/"]}