(()=>{var e={};e.id=465,e.ids=[465],e.modules={563:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(1120),n=r(4604),a=(0,s.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:s,namespace:a,onError:i=n.g,...o}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...o,onError:i,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),i=r(8692);function o(...[e]){return a((0,i.A)("useTranslations"),e)}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1195:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\src\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Navigation.tsx","default")},2234:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\sections\\HeroSection.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9697)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1434)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\app\\[locale]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4712:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\src\\components\\layout\\Footer.tsx","default")},6266:(e,t,r)=>{Promise.resolve().then(r.bind(r,5196)),Promise.resolve().then(r.bind(r,2830)),Promise.resolve().then(r.bind(r,3197)),Promise.resolve().then(r.bind(r,8570))},8570:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var s=r(687),n=r(7618),a=r(8521),i=r(9877),o=r(5814),l=r.n(o),c=r(3210);let d=c.forwardRef(function({title:e,titleId:t,...r},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"}))});var m=r(137);let u=c.forwardRef(function({title:e,titleId:t,...r},s){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))});function p(){let e=(0,n.c3)("hero"),t=(0,a.Ym)(),r={hidden:{opacity:0,y:30},visible:{opacity:1,y:0}};return(0,s.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-br from-military-black via-deep-charcoal to-steel-gray",children:[(0,s.jsx)("div",{className:"absolute inset-0 camo-pattern opacity-30"}),(0,s.jsx)("div",{className:"absolute inset-0",children:[...Array(20)].map((e,t)=>(0,s.jsx)(i.P.div,{className:"absolute w-1 h-1 bg-military-green/20 rounded-full",style:{left:`${100*Math.random()}%`,top:`${100*Math.random()}%`},animate:{y:[0,-20,0],opacity:[.2,.8,.2]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random()}},t))})]}),(0,s.jsxs)(i.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8,staggerChildren:.2}}},initial:"hidden",animate:"visible",className:"relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto",children:[(0,s.jsx)(i.P.div,{variants:{hidden:{opacity:0,scale:.8},visible:{opacity:1,scale:1}},transition:{duration:1,ease:"easeOut"},className:"mb-8 flex justify-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-32 h-32 md:w-40 md:h-40 bg-gradient-to-br from-yellow-600 via-yellow-500 to-yellow-700 rounded-full flex items-center justify-center military-glow-strong shadow-2xl",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-military-black mb-1",children:"ar"===t?"ود":"MD"}),(0,s.jsx)("div",{className:"text-xs md:text-sm font-semibold text-military-black",children:"ar"===t?"سوريا":"SYRIA"})]})}),(0,s.jsx)(i.P.div,{className:"absolute inset-0 w-32 h-32 md:w-40 md:h-40 bg-military-green/20 rounded-full blur-xl",animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}})]})}),(0,s.jsx)(i.P.h1,{variants:r,transition:{duration:.6,ease:"easeOut"},className:`text-4xl md:text-6xl lg:text-7xl font-bold mb-4 ${"ar"===t?"font-arabic-headings":"font-english-primary"}`,children:(0,s.jsx)("span",{className:"bg-gradient-to-r from-pure-white via-light-gray to-pure-white bg-clip-text text-transparent",children:e("title")})}),(0,s.jsx)(i.P.p,{variants:r,transition:{duration:.6,ease:"easeOut"},className:`text-xl md:text-2xl lg:text-3xl text-bright-green mb-6 font-semibold ${"ar"===t?"font-arabic-primary":"font-english-primary"}`,children:e("subtitle")}),(0,s.jsx)(i.P.p,{variants:r,transition:{duration:.6,ease:"easeOut"},className:`text-lg md:text-xl text-light-gray mb-12 max-w-4xl mx-auto leading-relaxed ${"ar"===t?"font-arabic-secondary":"font-english-secondary"}`,children:e("description")}),(0,s.jsxs)(i.P.div,{variants:r,transition:{duration:.6,ease:"easeOut"},className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,s.jsxs)(l(),{href:`/${t}/news`,className:"group flex items-center space-x-3 rtl:space-x-reverse bg-military-green hover:bg-bright-green text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 military-glow hover:military-glow-strong",children:[(0,s.jsx)(d,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:e("cta.news")})]}),(0,s.jsxs)(l(),{href:`/${t}/contact`,className:"group flex items-center space-x-3 rtl:space-x-reverse bg-transparent border-2 border-steel-gray hover:border-bright-green text-light-gray hover:text-bright-green px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:e("cta.contact")})]}),(0,s.jsxs)(l(),{href:`/${t}/emergency`,className:"group flex items-center space-x-3 rtl:space-x-reverse bg-warning-red hover:bg-red-700 text-pure-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red-900/50",children:[(0,s.jsx)(u,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:e("cta.emergency")})]})]})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-deep-charcoal to-transparent"})]})}},8621:(e,t,r)=>{Promise.resolve().then(r.bind(r,994)),Promise.resolve().then(r.bind(r,4712)),Promise.resolve().then(r.bind(r,1195)),Promise.resolve().then(r.bind(r,2234))},8692:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(9769),n=r(1120),a=r.t(n,2)["use".trim()];function i(e){var t=(0,s.A)();try{return a(t)}catch(t){throw t instanceof TypeError&&t.message.includes("Cannot read properties of null (reading 'use')")?Error(`\`${e}\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`,{cause:t}):t}}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(7413),n=r(563),a=r(2234),i=r(1195),o=r(4712);function l(){return(0,n.A)(),(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)(a.default,{}),(0,s.jsx)(o.default,{})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,825,625,768,216],()=>r(3291));module.exports=s})();