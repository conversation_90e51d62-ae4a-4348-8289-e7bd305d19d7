{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(ar|en)/:path*{(\\\\.json)}?", "originalSource": "/(ar|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "gQtcWOkKyTbjvKWoWIRXms23gY5ZlpwVZ3saBo/HHX0=", "__NEXT_PREVIEW_MODE_ID": "54592a92ae85dae95be0b8953eda6b97", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "748a4d32595b35352ff0bccee9fa1e1c8b0fcd9191fc7b47fcb5eac2d23a7e1e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0742c920d014f82097ca60783e884daa90b5a198198aff5dfb1fd8e3f7186801"}}}, "instrumentation": null, "functions": {}}